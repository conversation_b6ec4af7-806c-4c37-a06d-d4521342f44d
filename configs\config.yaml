# AI对话运维管理平台 - 配置文件

# 应用配置
app:
  name: "aiops-platform"
  env: "production"
  debug: false
  port: 8080
  version: "3.0.0"

# 数据库配置
database:
  path: "./data/aiops.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "1h"
  conn_max_idle_time: "10m"

# JWT配置
jwt:
  secret: "${AIOPS_JWT_SECRET}"  # 请通过环境变量设置
  access_token_ttl: "15m"
  refresh_token_ttl: "168h"
  issuer: "aiops-platform"
  max_concurrent_sessions: 5

# DeepSeek API配置
deepseek:
  api_key: "${AIOPS_DEEPSEEK_API_KEY}"  # 请通过环境变量设置真实的API密钥
  api_url: "https://api.deepseek.com"
  model: "deepseek-chat"
  timeout: "30s"
  max_retries: 3
  max_context_tokens: 4000
  temperature: 0.7
  top_p: 0.9

# 安全配置
security:
  encryption_key: "${AIOPS_ENCRYPTION_KEY}"  # 生产环境请通过环境变量设置32字节加密密钥，开发环境会自动使用默认值
  password_hash_cost: 12
  session_timeout: "24h"
  rate_limit:
    enabled: true
    global: "1000/min"
    per_user: "100/min"
    per_ip: "200/min"

# Redis配置
redis:
  enabled: false
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10 

# SSH配置
ssh:
  timeout: "30s"
  max_connections: 10
  idle_timeout: "5m"
  health_check_interval: "1m"

# 日志配置
log:
  level: "info"
  file: "./logs/aiops.log"
  max_size: 100
  retention_days: 30
  format: "json"

# 监控配置
metrics:
  enabled: true
  port: 9090
  path: "/metrics"

# AI智能Agent配置
ai:
  enable_intelligent_mode: true  # 启用智能Agent模式
  deepseek:
    api_key: "${AIOPS_DEEPSEEK_API_KEY}"
    base_url: "https://api.deepseek.com"
    model: "deepseek-chat"
    max_tokens: 4000
    temperature: 0.7

  # 智能Agent配置
  intelligent_agent:
    enable_auto_execution: true
    confidence_threshold: 0.7
    max_concurrent_tasks: 10
    default_timeout: "5m"
    enable_fallback: true

# Agent平台配置
agent:
  enabled: true
  max_agents: 50
  health_check_interval: "30s"
  registration_ttl: "1h"
  cleanup_interval: "10m"
  max_concurrent_requests: 10
  enable_auto_registration: true

# 高级缓存配置
cache:
  enabled: true
  l1_max_size: "100MB"
  l1_ttl: "5m"
  l2_max_size: "1GB"
  l2_ttl: "1h"
  l3_ttl: "24h"
  enable_compression: true
  enable_encryption: false
  cleanup_interval: "10m"

# 性能监控配置
performance:
  enabled: true
  collection_interval: "30s"
  enable_system_metrics: true
  enable_business_metrics: true
  enable_alerting: true
  metrics_retention: "24h"
  alert_cooldown: "5m"

# 负载均衡配置
load_balancer:
  enabled: true
  default_strategy: "intelligent"
  health_check_interval: "30s"
  health_check_timeout: "5s"
  max_retries: 3
  retry_interval: "1s"
  enable_metrics: true
  enable_auto_scaling: false
  load_threshold: 80.0

# 高级安全配置
advanced_security:
  enable_threat_detection: true
  enable_rate_limit: true
  enable_audit_log: true
  enable_encryption: true
  max_requests_per_minute: 100
  session_timeout: "30m"
  password_min_length: 8
  require_mfa: false
